using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Helpers;

/// <summary>
/// Helper class for RZW Savings calculations
/// </summary>
public static class RzwSavingsCalculationHelper
{
    /// <summary>
    /// Calculate raw APY (Annual Percentage Yield) from daily compound interest rate
    /// Returns the pure mathematical result without any formatting or rounding
    /// </summary>
    /// <param name="dailyInterestRate">Daily interest rate as decimal (e.g., 0.001 for 0.1%)</param>
    /// <returns>Raw APY as decimal ratio (e.g., 0.304567 for 30.4567% APY)</returns>
    public static decimal CalculateAPY(decimal dailyInterestRate)
    {
        // APY = (1 + daily_rate)^365 - 1
        return (decimal)(Math.Pow((double)(1 + dailyInterestRate), 365) - 1);
    }

    /// <summary>
    /// Get APY as percentage with high precision for internal calculations
    /// </summary>
    /// <param name="dailyInterestRate">Daily interest rate as decimal</param>
    /// <returns>APY as percentage with 8 decimal places (e.g., 30.45670000)</returns>
    public static decimal GetAPYAsPercentage(decimal dailyInterestRate)
    {
        var apy = CalculateAPY(dailyInterestRate);
        return Math.Round(apy * 100, 8, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// Get APY as floored percentage for conservative display purposes
    /// Use this method for display purposes only on EarnMoney page
    /// </summary>
    /// <param name="dailyInterestRate">Daily interest rate as decimal</param>
    /// <returns>Floored APY as percentage with 2 decimal places (e.g., 30.45)</returns>
    public static decimal GetAPYAsFlooredPercentage(decimal dailyInterestRate)
    {
        var apy = CalculateAPY(dailyInterestRate);
        var apyPercent = apy * 100;
        // Floor to 2 decimal places to avoid showing inflated returns
        return Math.Floor(apyPercent * 100) / 100m;
    }

    /// <summary>
    /// Calculate raw APY for a RZW Savings Plan
    /// </summary>
    /// <param name="plan">RZW Savings Plan</param>
    /// <returns>Raw APY as decimal ratio</returns>
    public static decimal CalculateAPY(RzwSavingsPlan plan)
    {
        return CalculateAPY(plan.InterestRate);
    }

    /// <summary>
    /// Get APY as percentage for a RZW Savings Plan (high precision)
    /// </summary>
    /// <param name="plan">RZW Savings Plan</param>
    /// <returns>APY as percentage with 8 decimal places</returns>
    public static decimal GetAPYAsPercentage(RzwSavingsPlan plan)
    {
        return GetAPYAsPercentage(plan.InterestRate);
    }

    /// <summary>
    /// Get APY as floored percentage for a RZW Savings Plan (display purposes)
    /// </summary>
    /// <param name="plan">RZW Savings Plan</param>
    /// <returns>Floored APY as percentage with 2 decimal places</returns>
    public static decimal GetAPYAsFlooredPercentage(RzwSavingsPlan plan)
    {
        return GetAPYAsFlooredPercentage(plan.InterestRate);
    }
    /// <summary>
    /// Calculate daily interest amount for a given principal and plan
    /// </summary>
    /// <param name="principalAmount">Principal amount in RZW</param>
    /// <param name="plan">RZW Savings Plan</param>
    /// <returns>Daily interest amount in RZW</returns>
    public static decimal CalculateDailyInterest(decimal principalAmount, RzwSavingsPlan plan)
    {
        return principalAmount * plan.InterestRate;
    }

    /// <summary>
    /// Calculate total interest for a given principal, plan and duration using compound interest
    /// </summary>
    /// <param name="principalAmount">Principal amount in RZW</param>
    /// <param name="plan">RZW Savings Plan</param>
    /// <param name="days">Number of days (optional, uses plan duration if not specified)</param>
    /// <returns>Total interest amount in RZW</returns>
    public static decimal CalculateTotalInterest(decimal principalAmount, RzwSavingsPlan plan, int? days = null)
    {
        var duration = days ?? plan.TermDuration;
        return CalculateCompoundInterest(principalAmount, plan.InterestRate, duration);
    }

    /// <summary>
    /// Calculate daily rate from annual rate based on term type
    /// </summary>
    /// <param name="annualRate">Annual interest rate</param>
    /// <param name="termType">Term type</param>
    /// <returns>Daily interest rate</returns>
    public static decimal CalculateDailyRate(decimal annualRate, string termType)
    {
        return termType switch
        {
            RzwSavingsTermType.Daily => annualRate, // Daily plan uses direct rate
            RzwSavingsTermType.Monthly => annualRate / 30m, // Monthly plan divided by 30
            RzwSavingsTermType.Yearly => annualRate / 365m, // Yearly plan divided by 365
            _ => 0m
        };
    }

    /// <summary>
    /// Calculate compound interest for a given principal, daily rate and duration
    /// Uses the formula: A = P * (1 + r)^n, where interest = A - P
    /// </summary>
    /// <param name="principalAmount">Principal amount in RZW</param>
    /// <param name="dailyRate">Daily interest rate as decimal</param>
    /// <param name="days">Number of days</param>
    /// <returns>Total compound interest amount in RZW</returns>
    public static decimal CalculateCompoundInterest(decimal principalAmount, decimal dailyRate, int days)
    {
        if (days <= 0 || dailyRate <= 0 || principalAmount <= 0) return 0;

        // Compound interest formula: A = P * (1 + r)^n
        var compoundMultiplier = Math.Pow((double)(1 + dailyRate), days);
        var finalAmount = principalAmount * (decimal)compoundMultiplier;
        var totalInterest = finalAmount - principalAmount;

        return Math.Round(totalInterest, 8, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// Calculate final amount (principal + compound interest) for a given principal, daily rate and duration
    /// </summary>
    /// <param name="principalAmount">Principal amount in RZW</param>
    /// <param name="dailyRate">Daily interest rate as decimal</param>
    /// <param name="days">Number of days</param>
    /// <returns>Final amount (principal + interest) in RZW</returns>
    public static decimal CalculateFinalAmount(decimal principalAmount, decimal dailyRate, int days)
    {
        if (days <= 0 || dailyRate <= 0 || principalAmount <= 0) return principalAmount;

        // Compound interest formula: A = P * (1 + r)^n
        var compoundMultiplier = Math.Pow((double)(1 + dailyRate), days);
        var finalAmount = principalAmount * (decimal)compoundMultiplier;

        // Debug logging to identify calculation issues
        System.Diagnostics.Debug.WriteLine($"CalculateFinalAmount: Principal={principalAmount}, Rate={dailyRate}, Days={days}, Multiplier={compoundMultiplier}, Result={finalAmount}");

        return Math.Round(finalAmount, 8, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// Calculate effective APY from principal, final amount and term duration
    /// For terms of exactly 365 days, this returns the actual percentage return.
    /// For other terms, it annualizes the return.
    /// </summary>
    /// <param name="principalAmount">Principal amount</param>
    /// <param name="finalAmount">Final amount after interest</param>
    /// <param name="termDays">Term duration in days</param>
    /// <returns>Effective APY as percentage</returns>
    public static decimal CalculateEffectiveAPY(decimal principalAmount, decimal finalAmount, int termDays)
    {
        if (principalAmount <= 0 || finalAmount <= principalAmount || termDays <= 0) return 0;

        // For exactly 365 days (1 year), return the actual percentage return
        if (termDays == 365)
        {
            var actualReturn = ((finalAmount - principalAmount) / principalAmount) * 100;
            return Math.Round(actualReturn, 2, MidpointRounding.AwayFromZero);
        }

        // For other terms, annualize the return: APY = ((Final/Principal)^(365/days) - 1) * 100
        var growthRatio = (double)(finalAmount / principalAmount);
        var annualizedGrowth = Math.Pow(growthRatio, 365.0 / termDays);
        var apy = (decimal)(annualizedGrowth - 1) * 100;

        return Math.Round(apy, 2, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// Format maximum RZW amount with localization support
    /// </summary>
    /// <param name="maxAmount">Maximum amount (nullable)</param>
    /// <param name="unlimitedText">Text to show when unlimited</param>
    /// <returns>Formatted string</returns>
    public static string FormatMaxAmount(decimal? maxAmount, string unlimitedText)
    {
        return maxAmount?.ToString("N0") ?? unlimitedText;
    }

    /// <summary>
    /// Get display text for maximum amount with RZW suffix
    /// </summary>
    /// <param name="maxAmount">Maximum amount (nullable)</param>
    /// <param name="unlimitedText">Text to show when unlimited</param>
    /// <returns>Formatted string with RZW suffix when applicable</returns>
    public static string GetMaxAmountDisplayText(decimal? maxAmount, string unlimitedText)
    {
        if (maxAmount.HasValue)
        {
            return $"{maxAmount.Value:N0} RZW";
        }
        return unlimitedText;
    }

    /// <summary>
    /// Format minimum RZW amount as integer with RZW suffix
    /// </summary>
    /// <param name="minAmount">Minimum amount</param>
    /// <returns>Formatted string with RZW suffix</returns>
    public static string GetMinAmountDisplayText(decimal minAmount)
    {
        return $"{minAmount:N0} RZW";
    }

    /// <summary>
    /// Calculate precise progress percentage based on seconds elapsed
    /// This provides much more accurate progress calculation, especially for short-term savings (1 day, etc.)
    /// </summary>
    /// <param name="startDate">Start date of the savings account</param>
    /// <param name="maturityDate">Maturity date of the savings account</param>
    /// <param name="currentDate">Current date (optional, uses UtcNow if not provided)</param>
    /// <returns>Progress percentage with high precision (0-100)</returns>
    public static decimal CalculatePreciseProgressPercentage(DateTime startDate, DateTime maturityDate, DateTime? currentDate = null)
    {
        var now = currentDate ?? DateTime.UtcNow;

        // Calculate total duration in seconds
        var totalSeconds = (maturityDate - startDate).TotalSeconds;

        // If total duration is zero or negative, return 100% (completed)
        if (totalSeconds <= 0) return 100m;

        // Calculate elapsed seconds
        var elapsedSeconds = (now - startDate).TotalSeconds;

        // Ensure elapsed seconds is not negative
        if (elapsedSeconds < 0) return 0m;

        // Calculate progress percentage
        var progressPercentage = (decimal)(elapsedSeconds / totalSeconds * 100);

        // Ensure progress doesn't exceed 100%
        return Math.Min(100m, Math.Max(0m, progressPercentage));
    }

    /// <summary>
    /// Calculate precise elapsed time in seconds
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="currentDate">Current date (optional, uses UtcNow if not provided)</param>
    /// <returns>Elapsed seconds</returns>
    public static double CalculateElapsedSeconds(DateTime startDate, DateTime? currentDate = null)
    {
        var now = currentDate ?? DateTime.UtcNow;
        return Math.Max(0, (now - startDate).TotalSeconds);
    }

    /// <summary>
    /// Calculate precise remaining time in seconds
    /// </summary>
    /// <param name="maturityDate">Maturity date</param>
    /// <param name="currentDate">Current date (optional, uses UtcNow if not provided)</param>
    /// <returns>Remaining seconds</returns>
    public static double CalculateRemainingSeconds(DateTime maturityDate, DateTime? currentDate = null)
    {
        var now = currentDate ?? DateTime.UtcNow;
        return Math.Max(0, (maturityDate - now).TotalSeconds);
    }

    /// <summary>
    /// Calculate early withdrawal penalty based on earned interest
    /// This is the centralized method for calculating early withdrawal penalties
    /// </summary>
    /// <param name="earnedInterest">The interest earned up to the withdrawal date</param>
    /// <param name="penaltyRate">The penalty rate as decimal (e.g., 0.1 for 10%)</param>
    /// <returns>Penalty amount to be deducted</returns>
    public static decimal CalculateEarlyWithdrawalPenalty(decimal earnedInterest, decimal penaltyRate)
    {
        if (earnedInterest <= 0 || penaltyRate <= 0) return 0;

        var penalty = earnedInterest * penaltyRate;
        return Math.Round(penalty, 8, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// Calculate net amount after early withdrawal (earned interest minus penalty)
    /// </summary>
    /// <param name="principalAmount">Original principal amount</param>
    /// <param name="earnedInterest">The interest earned up to the withdrawal date</param>
    /// <param name="penaltyRate">The penalty rate as decimal (e.g., 0.1 for 10%)</param>
    /// <returns>Net amount user will receive (principal + interest - penalty)</returns>
    public static decimal CalculateEarlyWithdrawalNetAmount(decimal principalAmount, decimal earnedInterest, decimal penaltyRate)
    {
        var penalty = CalculateEarlyWithdrawalPenalty(earnedInterest, penaltyRate);
        var netAmount = principalAmount + earnedInterest - penalty;
        return Math.Round(netAmount, 8, MidpointRounding.AwayFromZero);
    }
}
